#include "events_init.h"
#include <stdio.h>
#include "lvgl.h"
#include "ui_user_inc.h"
#include "esp_log.h"
#if LV_USE_GUIDER_SIMULATOR && LV_USE_FREEMASTER
#include "freemaster_client.h"
#endif

static const char *TAG = "wave_setup";

// 外部函数声明
extern void wave_set_display_type(wave_data_type_t type);


/**
 * @brief 下拉框选择事件处理
 */
static void wave_dropdown_event_handler(lv_event_t *e)
{
    lv_event_code_t code = lv_event_get_code(e);
    lv_obj_t *obj = lv_event_get_target(e);

    if (code == LV_EVENT_VALUE_CHANGED) {
        uint16_t selected = lv_dropdown_get_selected(obj);
        wave_data_type_t wave_type;

        // 根据下拉框选择映射到波形数据类型
        switch (selected) {
            case 0:
                wave_type = WAVE_DATA_CURRENT;
                break;
            case 1:
                wave_type = WAVE_DATA_SPEED;
                break;
            case 2:
                wave_type = WAVE_DATA_POSITION;
                break;
            case 3:
                wave_type = WAVE_DATA_VOLTAGE;
                break;
            case 4:
                wave_type = WAVE_DATA_TEMPERATURE;
                break;
            default:
                wave_type = WAVE_DATA_CURRENT;
                break;
        }

        // 切换波形显示类型
        wave_set_display_type(wave_type);

        ESP_LOGI(TAG, "Wave type changed to: %d", wave_type);
    }
}

/**
 * @brief 初始化波形页面
 */
static void wave_page_init(lv_ui *ui)
{
    // 更新下拉框选项，添加更多数据类型
    lv_dropdown_set_options(ui->Wave_Page_ddlist_1,
        "电流波形\n速度波形\n位置波形\n电压波形\n温度波形");

    // 设置默认选择
    lv_dropdown_set_selected(ui->Wave_Page_ddlist_1, 0);

    // 初始化图表设置
    if (ui->Wave_Page_chart_1) {
        // 设置图表为实时更新模式
        lv_chart_set_update_mode(ui->Wave_Page_chart_1, LV_CHART_UPDATE_MODE_SHIFT);

        // 设置点数
        lv_chart_set_point_count(ui->Wave_Page_chart_1, 100);

        // 设置初始范围（电流波形）
        lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y, 0, 20);
    }

    ESP_LOGI(TAG, "Wave page initialized");
}

void events_init_Wave_Page (lv_ui *ui)
{
    // 返回按钮事件
    lv_obj_add_event_cb(ui->Wave_Page_imgbtn_1, back_to_menu_event_handler, LV_EVENT_ALL, ui);

    // 下拉框选择事件
    lv_obj_add_event_cb(ui->Wave_Page_ddlist_1, wave_dropdown_event_handler, LV_EVENT_ALL, ui);

    // 初始化波形页面
    wave_page_init(ui);
}