#include "ui_user_inc.h"
#include "freertos/FreeRTOS.h"
#include "gui_port.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "esp_log.h"
#include "esp_timer.h"
#include "device_bsp.h"
#include "lvgl.h"

/*页面通知服务*/
#define TAG "ui notif"

// EXTERN
// extern void ui_notif_scr_main_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_weather_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_setting_webserver_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_network_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_setting_time_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_update_task(ui_notif_msg_t *msg);
extern void ui_notif_scr_can_connect_task(ui_notif_msg_t *msg);
extern void ui_notif_scr_wave_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_file_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_music_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_calendar_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_timer_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_layertop_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_recorder_task(ui_notif_msg_t *msg);
// extern void ui_notif_scr_chat_task(ui_notif_msg_t *msg);

extern void ui_notif_scr_can_connect_task(ui_notif_msg_t *msg);

// STATIC
static void _notif_serv_task(void *pvParameters);
static void _notif_serv_destroy(void);

typedef struct
{
    QueueHandle_t serv_q;
    bool running;
} ui_notif_service_t;

ui_notif_service_t ui_notif_serv_param;
// periph_service_handle_t ui_notif_service;

void ui_notif_service_init()
{
    ui_notif_serv_param.running = false;
    ui_notif_serv_param.serv_q = xQueueCreate(200, sizeof(ui_notif_msg_t));


    xTaskCreatePinnedToCore(_notif_serv_task, "ui_notif_serv", 8 * 1024, (void *)&ui_notif_serv_param, 4, NULL, 1);

}

void ui_notif_service_commit(ui_notif_msg_t *msg)
{
    BaseType_t xReturn = pdPASS;

    if (ui_notif_serv_param.serv_q) {
        xReturn = xQueueSend(ui_notif_serv_param.serv_q, msg, 0);
        if (xReturn != pdTRUE) {
            // ESP_LOGW("ui_notif", "Queue send failed, queue may be full");
        }
    }
}

/***********************************STATIC*************************************/
static void _notif_serv_destroy()
{
    ui_notif_serv_param.running = false;

    if (ui_notif_serv_param.serv_q)
    {
        vQueueDelete(ui_notif_serv_param.serv_q);
    }
}

static void _notif_serv_task(void *pvParameters)
{
    ui_notif_service_t *service = (ui_notif_service_t *)pvParameters;

    BaseType_t xReturn = pdFALSE;
    ui_notif_msg_t msg = {0};
    service->running = true;
    while (service->running)
    {
        xReturn = xQueueReceive(service->serv_q, &msg, portMAX_DELAY);
        if (xReturn == pdTRUE)
        {
            // if (msg.type == UI_NOTIF_SERV_DESTROY)
            // {
            //     hal_service_destroy(ui_notif_service);
            //     continue;
            // }

            ui_scr_t *scr = ui_scr_get_cur();

            if (scr == NULL)
            {
                continue;
            }

            if (lvgl_port_lock(0))
            {
                // layer top msg
                // ui_notif_scr_layertop_task(&msg);
                
                switch ((int)scr->id)
                {
                
                case UI_SCR_ID_MENU_PAGE:
                    // ui_notif_scr_menu_task(&msg);
                    break;
                case UI_SCR_ID_CAN_CONNET_PAGE:
                    ui_notif_scr_can_connect_task(&msg);
                    break;
                case UI_SCR_ID_BT_PAGE:
                    // ui_notif_scr_network_task(&msg);
                    break;
                case UI_SCR_ID_SETTINGS_PAGE:
                    // ui_notif_scr_setting_time_task(&msg);
                    break;
                case UI_SCR_ID_PID_PAGE:
                    // ui_notif_scr_update_task(&msg);
                    break;
                case UI_SCR_ID_AI_PAGE:
                    // ui_notif_scr_file_task(&msg);
                    break;
                case UI_SCR_ID_WAVE_PAGE:
                    ui_notif_scr_wave_task(&msg);
                    break;
                case UI_SCR_ID_WIFI_PAGE:
                    // ui_notif_scr_calendar_task(&msg);
                    break;
                case UI_SCR_ID_DEVOCE_INFO_PAGE:
                    // ui_notif_scr_timer_task(&msg);
                    break;
                case UI_SCR_ID_UPDATE_PAGE:
                    // ui_notif_scr_recorder_task(&msg);
                    break;
                default:
                    break;
                }
                // 释放LVGL锁
                lvgl_port_unlock();
            }
        }

        vTaskDelay(10);
    }

    ESP_LOGI(TAG, "ui notif service stopped!");
    vTaskDelete(NULL);
}
