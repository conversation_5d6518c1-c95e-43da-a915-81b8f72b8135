#include "ui_user_inc.h"
#include "esp_log.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include <string.h>

static const char *TAG = "ui_wave";

// 波形数据缓存配置
#define WAVE_BUFFER_SIZE 100        // 缓存数据点数量

// 波形数据缓存
static int32_t wave_data_buffer[WAVE_DATA_MAX][WAVE_BUFFER_SIZE];
static uint16_t wave_buffer_index[WAVE_DATA_MAX] = {0};
static uint32_t last_update_time = 0;
static wave_data_type_t current_wave_type = WAVE_DATA_CURRENT; // 当前显示的波形类型

// 波形配置表 - 便于扩展新的数据类型
typedef struct {
    wave_data_type_t type;
    const char *name;
    lv_color_t color;
    int32_t min_range;
    int32_t max_range;
    float scale_factor;  // 缩放因子
} wave_config_t;

static const wave_config_t wave_configs[WAVE_DATA_MAX] = {
    [WAVE_DATA_CURRENT] = {
        .type = WAVE_DATA_CURRENT,
        .name = "电流波形",
        .color = LV_COLOR_MAKE(255, 0, 52),  // 红色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f
    },
    [WAVE_DATA_SPEED] = {
        .type = WAVE_DATA_SPEED,
        .name = "速度波形",
        .color = LV_COLOR_MAKE(47, 53, 218), // 蓝色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f  // 速度数据较大，需要缩放
    },
    [WAVE_DATA_POSITION] = {
        .type = WAVE_DATA_POSITION,
        .name = "位置波形",
        .color = LV_COLOR_MAKE(0, 255, 0),   // 绿色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f // 位置数据很大，需要更大缩放
    },
    [WAVE_DATA_VOLTAGE] = {
        .type = WAVE_DATA_VOLTAGE,
        .name = "电压波形",
        .color = LV_COLOR_MAKE(255, 165, 0), // 橙色
        .min_range = 0,
        .max_range = 150,
        .scale_factor = 1.0f
    },
    [WAVE_DATA_TEMPERATURE] = {
        .type = WAVE_DATA_TEMPERATURE,
        .name = "温度波形",
        .color = LV_COLOR_MAKE(255, 0, 255), // 紫色
        .min_range = 0,
        .max_range = 100,
        .scale_factor = 1.0f
    }
};

/**
 * @brief 添加波形数据到缓存
 */
static void wave_add_data_to_buffer(wave_data_type_t type, int32_t value)
{
    if (type >= WAVE_DATA_MAX) {
        return;
    }

    // 应用缩放因子
    int32_t scaled_value = (int32_t)(value * wave_configs[type].scale_factor);

    // 添加到缓存
    wave_data_buffer[type][wave_buffer_index[type]] = scaled_value;
    wave_buffer_index[type] = (wave_buffer_index[type] + 1) % WAVE_BUFFER_SIZE;
}

/**
 * @brief 更新图表显示
 */
static void wave_update_chart_display(void)
{
    lv_ui *ui = &guider_ui;

    if (!ui->Wave_Page_chart_1 || current_wave_type >= WAVE_DATA_MAX) {
        return;
    }

    const wave_config_t *config = &wave_configs[current_wave_type];

    // 设置图表范围
    lv_chart_set_range(ui->Wave_Page_chart_1, LV_CHART_AXIS_PRIMARY_Y,
                       config->min_range, config->max_range);

    // 清空现有数据，从头开始显示
    lv_chart_set_all_value(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_0, LV_CHART_POINT_NONE);

    // 更新数据系列颜色
    lv_chart_set_series_color(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_0, config->color);

    // 刷新图表
    lv_chart_refresh(ui->Wave_Page_chart_1);
}

/**
 * @brief 设置当前显示的波形类型
 */
void wave_set_display_type(wave_data_type_t type)
{
    if (type >= WAVE_DATA_MAX) {
        return;
    }

    // 清空当前类型的缓存数据，从头开始显示
    // wave_buffer_index[type] = 0;
    // memset(wave_data_buffer[type], 0, sizeof(wave_data_buffer[type]));

    current_wave_type = type;
    wave_update_chart_display();

    ESP_LOGI(TAG, "Switch to wave type: %s", wave_configs[type].name);
}

/**
 * @brief 获取当前显示的波形类型
 */
wave_data_type_t wave_get_display_type(void)
{
    return current_wave_type;
}

/**
 * @brief 获取波形配置
 */
const wave_config_t* wave_get_config(wave_data_type_t type)
{
    if (type >= WAVE_DATA_MAX) {
        return NULL;
    }
    return &wave_configs[type];
}

/**
 * @brief 波形数据更新处理
 */
void ui_scr_wave_data_update(ui_notif_msg_t *msg)
{
    static uint32_t recv_call_count = 0;           // 接收调用次数统计
    static uint32_t recv_valid_count = 0;          // 有效数据次数统计
    static uint32_t recv_chart_update_count = 0;   // 图表更新次数统计
    static uint32_t last_recv_freq_report_time = 0; // 上次频率报告时间

    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    recv_call_count++;  // 每次调用都计数

    if (!msg || !msg->user_data) {
        return;
    }

    wave_data_t *wave_data = (wave_data_t *)msg->user_data;

    if (!wave_data->valid || wave_data->type >= WAVE_DATA_MAX) {
        ESP_LOGW(TAG, "Invalid wave data: valid=%d, type=%d", wave_data->valid, wave_data->type);
        return;
    }

    recv_valid_count++;  // 有效数据计数

    // 每5秒报告一次接收频率统计
    if (current_time - last_recv_freq_report_time >= 5000) {
        float recv_freq = (float)recv_call_count * 1000.0f / (current_time - last_recv_freq_report_time);
        float valid_freq = (float)recv_valid_count * 1000.0f / (current_time - last_recv_freq_report_time);
        float chart_freq = (float)recv_chart_update_count * 1000.0f / (current_time - last_recv_freq_report_time);
        ESP_LOGI(TAG, "[WAVE_RECV_FREQ] Total: %.1f Hz, Valid: %.1f Hz, Chart: %.1f Hz, Valid: %lu/%lu (%.1f%%)",
                 recv_freq, valid_freq, chart_freq, recv_valid_count, recv_call_count,
                 (float)recv_valid_count * 100.0f / recv_call_count);

        // 重置计数器
        recv_call_count = 0;
        recv_valid_count = 0;
        recv_chart_update_count = 0;
        last_recv_freq_report_time = current_time;
    }

    // 减少日志输出频率
    static uint32_t recv_log_counter = 0;
    if (++recv_log_counter % 20 == 0) {  // 每20次打印一次
        ESP_LOGI(TAG, "Recv wave data: type=%d, value=%ld, current_type=%d, counter=%lu",
                 wave_data->type, wave_data->value, current_wave_type, recv_log_counter);
    }

    // 限制更新频率
    // uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    // if (current_time - last_update_time < WAVE_UPDATE_INTERVAL_MS) {
    //     return;
    // }
    // last_update_time = current_time;

    // 添加数据到缓存
    wave_add_data_to_buffer(wave_data->type, wave_data->value);

    // 如果是当前显示的数据类型，更新图表
    if (wave_data->type == current_wave_type) {
        lv_ui *ui = &guider_ui;
        if (ui->Wave_Page_chart_1 && ui->Wave_Page_chart_1_0) {
            // 应用缩放因子
            int32_t scaled_value = (int32_t)(wave_data->value * wave_configs[wave_data->type].scale_factor);
            lv_chart_set_next_value(ui->Wave_Page_chart_1, ui->Wave_Page_chart_1_0, scaled_value);
            recv_chart_update_count++;  // 图表更新计数
        }
    }
}

void ui_notif_scr_wave_task(ui_notif_msg_t *msg)
{
    switch ((int)msg->type)
    {
    case UI_NOTIF_WAVE_DATA_UPDATE:
        ui_scr_wave_data_update(msg);
        break;

    default:
        break;
    }
}