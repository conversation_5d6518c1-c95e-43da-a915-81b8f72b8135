#include <stdio.h>
#include <string.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/semphr.h"
#include "freertos/queue.h"
#include "esp_log.h"
#include "esp_err.h"
#include "driver/twai.h"
#include "device_bsp.h"

// 引入UI相关头文件
#include "ui_user_inc.h"


// 日志标签
static const char *TAG = "CANopen";

// CAN数据更新函数声明
static void canopen_send_data_to_ui(void);
static void canopen_send_status_to_ui(bool connected);
static void canopen_send_wave_data_to_ui(void);

// TWAI GPIO配置
#define TWAI_TX_GPIO_NUM    11
#define TWAI_RX_GPIO_NUM    10

// 全局变量定义
canopen_config_t canopen_config = {
    .node_id = DEFAULT_NODE_ID,
    .tpdo_period = DEFAULT_TPDO_PERIOD,
    .write_access = true,
    .error_register = 0
};

uint8_t tpdo1_data[8] = {0};       // TPDO1数据缓冲区
uint8_t rpdo1_data[8] = {0};       // RPDO1数据缓冲区

// 控制目标值（从RPDO接收）
int32_t target_position = 0;       // 目标位置
int32_t target_speed = 0;          // 目标速度
int32_t target_iq = 0;             // 目标转矩电流

// 实际值（通过TPDO发送）
int32_t real_speed_filter = 0;     // 实际速度（滤波后）
int32_t pos_actual = 0;            // 实际位置

// 内部变量
static TaskHandle_t tpdo_task_handle = NULL;
static TaskHandle_t receive_task_handle = NULL;
static QueueHandle_t rx_queue = NULL;
static bool canopen_started = false;

// TWAI配置
static twai_timing_config_t timing_config;
static twai_filter_config_t filter_config = {
    .acceptance_code = 0,
    .acceptance_mask = 0xFFFFFFFF,  // 接收所有消息
    .single_filter = false
};
static twai_general_config_t general_config;

/**
 * @brief 初始化CANopen协议栈
 */
esp_err_t canopen_init(uint8_t node_id, uint16_t tpdo_period)
{
    esp_err_t ret;
    
    canopen_config.node_id = node_id;
    canopen_config.tpdo_period = tpdo_period;
    
    // 初始化TWAI配置
    twai_timing_config_t temp_timing = TWAI_TIMING_CONFIG_500KBITS();
    timing_config = temp_timing;
    
    twai_general_config_t temp_general = TWAI_GENERAL_CONFIG_DEFAULT(TWAI_TX_GPIO_NUM, TWAI_RX_GPIO_NUM, TWAI_MODE_NORMAL);
    general_config = temp_general;
    
    // 创建接收队列
    rx_queue = xQueueCreate(10, sizeof(twai_message_t));
    if (rx_queue == NULL) {
        return ESP_ERR_NO_MEM;
    }
    
    // 安装TWAI驱动
    ret = twai_driver_install(&general_config, &timing_config, &filter_config);
    if (ret != ESP_OK) {
        return ret;
    }
    
    return ESP_OK;
}

/**
 * @brief 启动CANopen通信
 */
esp_err_t canopen_start(void)
{
    esp_err_t ret;
    
    if (canopen_started) {
        ESP_LOGW(TAG, "CANopen already started");
        return ESP_OK;
    }
    
    // 启动TWAI驱动
    ret = twai_start();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start TWAI: %s", esp_err_to_name(ret));
        return ret;
    }
    
    // 创建TPDO发送任务
    // xTaskCreatePinnedToCore(canopen_tpdo_task, "canopen_tpdo", 4096, NULL, 2, &tpdo_task_handle, 0);
    
    // 创建接收处理任务（降低优先级）
    xTaskCreatePinnedToCore(canopen_receive_task, "canopen_rx", 4096, NULL, 3, &receive_task_handle, 0);
    
    canopen_started = true;
    ESP_LOGI(TAG, "CANopen started");
    return ESP_OK;
}

/**
 * @brief 停止CANopen通信
 */
esp_err_t canopen_stop(void)
{
    if (!canopen_started) {
        ESP_LOGW(TAG, "CANopen not started");
        return ESP_OK;
    }
    
    // 删除任务
    if (tpdo_task_handle != NULL) {
        vTaskDelete(tpdo_task_handle);
        tpdo_task_handle = NULL;
    }
    
    if (receive_task_handle != NULL) {
        vTaskDelete(receive_task_handle);
        receive_task_handle = NULL;
    }
    
    // 停止TWAI驱动
    esp_err_t ret = twai_stop();
    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to stop TWAI: %s", esp_err_to_name(ret));
        return ret;
    }
    
    canopen_started = false;
    ESP_LOGI(TAG, "CANopen stopped");
    return ESP_OK;
}

/**
 * @brief 处理TPDO发送
 */
void canopen_process_tpdo(void)
{
    twai_message_t tx_message = {
        .extd = 0,                                  // 标准帧
        .rtr = 0,                                   // 数据帧
        .ss = 0,                                    // 非单次发送
        .self = 0,                                  // 非自接收
        .dlc_non_comp = 0,                          // DLC有效
        .identifier = TPDO1_BASE_ID + canopen_config.node_id,  // TPDO1 ID
        .data_length_code = 8,                      // 8字节数据
    };
    
    // 打包实际速度到TPDO1数据（前4字节）
    tpdo1_data[0] = (real_speed_filter >> 24) & 0xFF;
    tpdo1_data[1] = (real_speed_filter >> 16) & 0xFF;
    tpdo1_data[2] = (real_speed_filter >> 8) & 0xFF;
    tpdo1_data[3] = real_speed_filter & 0xFF;
    
    // 打包实际位置到TPDO1数据（后4字节）
    tpdo1_data[4] = (pos_actual >> 24) & 0xFF;
    tpdo1_data[5] = (pos_actual >> 16) & 0xFF;
    tpdo1_data[6] = (pos_actual >> 8) & 0xFF;
    tpdo1_data[7] = pos_actual & 0xFF;
    
    // 复制数据到发送消息
    memcpy(tx_message.data, tpdo1_data, 8);
    
    // 发送TPDO消息
    esp_err_t ret = twai_transmit(&tx_message, pdMS_TO_TICKS(10));
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to transmit TPDO1: %s", esp_err_to_name(ret));
    } else {
        // 发送成功，更新UI显示
        // canopen_send_data_to_ui();

        // 发送波形数据到UI
        canopen_send_wave_data_to_ui();
    }
}

/**
 * @brief 处理接收到的RPDO消息
 */
void canopen_process_rpdo(twai_message_t *message)
{
    uint16_t rpdo_type = message->identifier & 0xF00;  // 获取RPDO类型
    uint8_t node_id = message->identifier & 0x07F;     // 获取节点ID
    ESP_LOGI(TAG, "Received RPDO type: 0x%03X from node ID: %d", rpdo_type, node_id);
    if (node_id != canopen_config.node_id) {
        return;  // 不是发给当前节点的消息
    }
    
    switch (rpdo_type) {
        case RPDO1_BASE_ID:  // 0x200 - 位置控制
            target_position = (message->data[4] << 24) + 
                             (message->data[5] << 16) + 
                             (message->data[6] << 8) + 
                             message->data[7];
            ESP_LOGI(TAG, "Received position target: %ld", target_position);
            break;
            
        case RPDO2_BASE_ID:  // 0x300 - 速度控制
            target_speed = (message->data[4] << 24) + 
                          (message->data[5] << 16) + 
                          (message->data[6] << 8) + 
                          message->data[7];
            ESP_LOGI(TAG, "Received speed target: %ld", target_speed);
            break;
            
        case RPDO3_BASE_ID:  // 0x400 - 转矩控制
            target_iq = (message->data[4] << 24) + 
                       (message->data[5] << 16) + 
                       (message->data[6] << 8) + 
                       message->data[7];
            ESP_LOGI(TAG, "Received torque target: %ld", target_iq);
            break;
            
        default:
            ESP_LOGW(TAG, "Unknown RPDO type: 0x%03X", rpdo_type);
            break;
    }
    
    // 保存到RPDO1缓冲区
    memcpy(rpdo1_data, message->data, 8);
}

/**
 * @brief 处理SDO请求
 */
uint8_t canopen_sdo_process(twai_message_t *message)
{
    dict_object_t sdo_obj;
    uint8_t state = 0;
    
    twai_message_t tx_message = {
        .extd = 0,
        .rtr = 0,
        .ss = 0,
        .self = 0,
        .dlc_non_comp = 0,
        .identifier = SDO_RSP_BASE_ID + canopen_config.node_id,  // SDO响应ID
        .data_length_code = 8,
    };
    
    ESP_LOGI(TAG, "Processing SDO request, command: 0x%02X", message->data[0]);
    
    switch (message->data[0]) {
        case SDO_W:    // SDO写命令
        case SDO_W_1:  // SDO写1字节
        case SDO_W_2:  // SDO写2字节
        case SDO_W_4:  // SDO写4字节
            if (canopen_config.write_access) {
                sdo_obj.index = message->data[1] + (message->data[2] << 8);
                sdo_obj.sub_index = message->data[3];
                sdo_obj.od_pointer = &message->data[4];
                state = canopen_write_od(sdo_obj);
                
                // 准备响应
                memcpy(&tx_message.data[1], &message->data[1], 7);
                tx_message.data[0] = (state == 1) ? 0x60 : 0x80;  // 成功/失败响应
            } else {
                // 仅允许特定参数写入
                sdo_obj.index = message->data[1] + (message->data[2] << 8);
                sdo_obj.sub_index = message->data[3];
                sdo_obj.od_pointer = &message->data[4];
                
                if (sdo_obj.index == 0x8888) {  // 特殊参数
                    state = canopen_write_od(sdo_obj);
                }
                
                memcpy(&tx_message.data[1], &message->data[1], 7);
                tx_message.data[0] = (state == 1) ? 0x60 : 0x80;
            }
            break;
            
        case SDO_R:    // SDO读命令
        case SDO_R_1:  // SDO读1字节
        case SDO_R_2:  // SDO读2字节
        case SDO_R_4:  // SDO读4字节
            sdo_obj.index = message->data[1] + (message->data[2] << 8);
            sdo_obj.sub_index = message->data[3];
            sdo_obj.od_pointer = &message->data[4];
            state = canopen_read_od(sdo_obj);
            
            // 准备响应
            memcpy(&tx_message.data[1], &message->data[1], 7);
            tx_message.data[0] = (state == 1) ? 0x60 : 0x80;  // 成功/失败响应
            break;
            
        default:
            ESP_LOGW(TAG, "Unknown SDO command: 0x%02X", message->data[0]);
            return 0;
    }
    
    // 发送SDO响应
    esp_err_t ret = twai_transmit(&tx_message, pdMS_TO_TICKS(10));
    if (ret != ESP_OK) {
        ESP_LOGW(TAG, "Failed to transmit SDO response: %s", esp_err_to_name(ret));
    }
    
    return state;
}

/**
 * @brief 写对象字典（简化实现）
 */
uint8_t canopen_write_od(dict_object_t obj)
{
    ESP_LOGI(TAG, "Write OD - Index: 0x%04X, SubIndex: 0x%02X", obj.index, obj.sub_index);
    
    // 这里应该根据实际的对象字典实现写操作
    // 示例：处理一些常用的参数
    switch (obj.index) {
        case 0x8888:  // 示例参数
            ESP_LOGI(TAG, "Writing special parameter 0x8888");
            return 1;  // 成功
            
        // 添加更多对象字典项...
        default:
            ESP_LOGW(TAG, "Unsupported OD write index: 0x%04X", obj.index);
            return 0;  // 失败
    }
}

/**
 * @brief 读对象字典（简化实现）
 */
uint8_t canopen_read_od(dict_object_t obj)
{
    ESP_LOGI(TAG, "Read OD - Index: 0x%04X, SubIndex: 0x%02X", obj.index, obj.sub_index);
    
    // 这里应该根据实际的对象字典实现读操作
    // 示例：返回一些常用的参数
    switch (obj.index) {
        case 0x1000:  // 设备类型
            *(uint32_t*)obj.od_pointer = 0x00000000;
            return 1;  // 成功
            
        case 0x1001:  // 错误寄存器
            *obj.od_pointer = canopen_config.error_register;
            return 1;  // 成功
            
        // 添加更多对象字典项...
        default:
            ESP_LOGW(TAG, "Unsupported OD read index: 0x%04X", obj.index);
            return 0;  // 失败
    }
}

/**
 * @brief TPDO发送任务
 */
void canopen_tpdo_task(void *pvParameters)
{
    TickType_t last_wake_time = xTaskGetTickCount();
    const TickType_t period = pdMS_TO_TICKS(canopen_config.tpdo_period);
    
    ESP_LOGI(TAG, "TPDO task started, period: %d ms", canopen_config.tpdo_period);
    
    while (1) {
        canopen_process_tpdo();
        vTaskDelayUntil(&last_wake_time, period);
    }
}

/**
 * @brief CAN接收任务
 */
void canopen_receive_task(void *pvParameters)
{
    twai_message_t rx_message;
    
    ESP_LOGI(TAG, "CANopen receive task started");
    
    // 发送连接状态到UI
    canopen_send_status_to_ui(true);
    
    while (1) {
        // 接收CAN消息
        esp_err_t ret = twai_receive(&rx_message, portMAX_DELAY);
        if (ret == ESP_OK) {
            // 打印完整的数据帧
            // ESP_LOGI(TAG, "Received CAN Frame:");
            // ESP_LOGI(TAG, "  - ID: 0x%03lX (%ld)", rx_message.identifier, rx_message.identifier);
            // ESP_LOGI(TAG, "  - DLC: %d", rx_message.data_length_code);
            // ESP_LOGI(TAG, "  - Extended: %s", rx_message.extd ? "Yes" : "No");
            // ESP_LOGI(TAG, "  - RTR: %s", rx_message.rtr ? "Yes" : "No");
            // if (rx_message.data_length_code > 0) {
            //     ESP_LOG_BUFFER_HEX_LEVEL(TAG, rx_message.data, rx_message.data_length_code, ESP_LOG_INFO);
            // }

            // 判断消息类型并处理
            uint16_t msg_type = rx_message.identifier & 0xF80;  // 获取消息类型（高7位）
            uint8_t node_id = rx_message.identifier & 0x07F;    // 获取节点ID（低7位）
            
            // ESP_LOGI(TAG, "Message routing - ID: 0x%03lX, msg_type: 0x%03X, node_id: %d, current_node: %d", 
            //          rx_message.identifier, msg_type, node_id, canopen_config.node_id);
            
            // 处理发给当前节点的消息（RPDO、SDO）
            if (node_id == canopen_config.node_id) {
                // ESP_LOGI(TAG, "Message for current node");
                if (msg_type >= RPDO1_BASE_ID && msg_type <= RPDO3_BASE_ID) {
                    // 处理RPDO消息
                    canopen_process_rpdo(&rx_message);
                } else if ((rx_message.identifier & 0xF80) == SDO_REQ_BASE_ID) {
                    // 处理SDO请求
                    canopen_sdo_process(&rx_message);
                } else if (msg_type == TPDO1_BASE_ID) {
                    // 如果是自己的TPDO消息，也可以处理（用于调试或自检）
                    // ESP_LOGI(TAG, "Processing own TPDO1 message");
                    canopen_process_received_tpdo(&rx_message);
                } else {
                    ESP_LOGW(TAG, "Unknown message type for current node: 0x%03X", msg_type);
                }
            }
            // 处理来自其他节点的TPDO消息
            else if (msg_type == TPDO1_BASE_ID) {
                ESP_LOGI(TAG, "Processing TPDO1 from other node");
                // 处理其他节点发送的TPDO1消息
                // esp_log_buffer_hex(TAG, rx_message.data, rx_message.data_length_code);
                canopen_process_received_tpdo(&rx_message);
            } else {
                ESP_LOGI(TAG, "Processing other TPDO message");
                // 处理接收到的TPDO消息（来自其他节点）
                canopen_process_received_tpdo(&rx_message);
            }
            
            // 接收成功，更新UI连接状态
            canopen_send_status_to_ui(true);
            
            // 让出CPU时间，避免看门狗超时
            vTaskDelay(pdMS_TO_TICKS(50));
        } else {
            ESP_LOGW(TAG, "Failed to receive message: %s", esp_err_to_name(ret));
            // 接收失败，更新UI断开状态
            canopen_send_status_to_ui(false);
            vTaskDelay(pdMS_TO_TICKS(50));
        }
    }
}

/**
 * @brief 处理接收到的TPDO消息（来自其他节点）
 */
void canopen_process_received_tpdo(twai_message_t *message)
{
    static uint32_t message_counter = 0;  // 消息计数器
    uint16_t tpdo_type = message->identifier & 0xF80;  // 获取TPDO类型
    uint8_t source_node_id = message->identifier & 0x07F;  // 获取源节点ID
    
    message_counter++;

    switch (tpdo_type) {
        case TPDO1_BASE_ID:  // 0x180 - TPDO1
            if (message->data_length_code == 8) {
                // 解析速度数据（前4字节）- 大端序
                int32_t remote_speed = (message->data[0] << 24) + 
                                      (message->data[1] << 16) + 
                                      (message->data[2] << 8) + 
                                      message->data[3];
                
                // 解析位置数据（后4字节）- 大端序
                int32_t remote_position = (message->data[4] << 24) + 
                                         (message->data[5] << 16) + 
                                         (message->data[6] << 8) + 
                                         message->data[7];
                                         
                // lv_label_set_text(ui->Can_Connet_page_Speed, "50Hz");
                // 每10条消息打印一次，减少日志输出
                // if (message_counter % 10 == 0) {
                //     ESP_LOGI(TAG, "Node %d [#%lu] - Speed: %ld, Position: %ld", 
                //              source_node_id, message_counter, remote_speed, remote_position);
                // } 
                // 可以在这里添加对接收到的数据的处理逻辑
                // 例如：存储到数组、触发回调函数等              
                // 更新全局变量，供UI显示使用
                real_speed_filter = remote_speed;
                pos_actual = remote_position;
                
                // 发送数据到UI
                canopen_send_data_to_ui();

                // 发送波形数据到UI
                canopen_send_wave_data_to_ui();
            }
            break;
            
        default:
            ESP_LOGW(TAG, "Unknown TPDO type: 0x%03X from node %d", tpdo_type, source_node_id);
            break;
    }
    
}

/**
 * @brief 发送CAN数据到UI显示
 */
static void canopen_send_data_to_ui(void)
{
    // static uint32_t last_update_time = 0;
    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    
    // 每100ms更新一次UI，避免过于频繁
    // if (current_time - last_update_time < 100) {
    //     return;
    // }
    // last_update_time = current_time;

    static can_display_data_t can_data;  // 使用静态变量避免局部变量被销毁

    can_data.voltage = 100;      // 电压值
    can_data.current = 200;      // 电流值
    can_data.temperature = 300;     // 温度值
    can_data.speed = real_speed_filter;                 // 实际速度
    can_data.position = pos_actual;              // 实际位置
    can_data.timestamp = current_time;    // 时间戳
    can_data.valid = true;

    // 调试：打印内存地址和发送时的值
    // ESP_LOGI(TAG, "[SEND] can_data addr: %p, ui_msg addr: %p, voltage: %ld",
    //          &can_data, &ui_msg, can_data.voltage);
    
    ui_notif_msg_t ui_msg = {
        .type = UI_NOTIF_CAN_DATA_UPDATE,
        .user_data = &can_data
    };
    
    // 发送到UI通知服务
    ui_notif_service_commit(&ui_msg);

}

/**
 * @brief 发送CAN状态到UI显示
 */
static void canopen_send_status_to_ui(bool connected)
{
    static can_status_data_t can_status = {0};
    
    can_status.connected = connected;
    if (connected) {
        can_status.msg_count++;
    } else {
        can_status.error_count++;
    }
    
    ui_notif_msg_t ui_msg = {
        .type = UI_NOTIF_CAN_STATUS_UPDATE,
        .user_data = &can_status
    };
    
    // 发送到UI通知服务
    ui_notif_service_commit(&ui_msg);
}

/**
 * @brief 发送波形数据到UI显示
 */
static void canopen_send_wave_data_to_ui(void)
{
    static uint32_t last_wave_update_time = 0;
    static uint32_t send_call_count = 0;           // 调用次数统计
    static uint32_t send_actual_count = 0;         // 实际发送次数统计
    static uint32_t last_freq_report_time = 0;     // 上次频率报告时间

    uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;
    send_call_count++;  // 每次调用都计数

    // 每5秒报告一次频率统计
    if (current_time - last_freq_report_time >= 5000) {
        float call_freq = (float)send_call_count * 1000.0f / (current_time - last_freq_report_time);
        float actual_freq = (float)send_actual_count * 1000.0f / (current_time - last_freq_report_time);
        ESP_LOGI(TAG, "[WAVE_SEND_FREQ] Call: %.1f Hz, Actual: %.1f Hz, Filtered: %lu/%lu (%.1f%%)",
                 call_freq, actual_freq, send_actual_count, send_call_count,
                 (float)send_actual_count * 100.0f / send_call_count);

        // 重置计数器
        send_call_count = 0;
        send_actual_count = 0;
        last_freq_report_time = current_time;
    }

    // 限制更新频率，避免队列过载
    if (current_time - last_wave_update_time < 100) {  // 100ms更新一次
        return;
    }
    last_wave_update_time = current_time;
    send_actual_count++;  // 实际发送计数


    // 生成一些模拟数据进行测试
    static int32_t voltage_base = 110;
    static int32_t current_base = 10;
    static int32_t temp_base = 25;
    static int32_t speed_base = 100;
    static int32_t position_base = 500;

    // 发送多种类型的波形数据 - 使用静态数组避免生命周期问题
    static wave_data_t wave_data_list[5];

    wave_data_list[0] = (wave_data_t){
        .type = WAVE_DATA_CURRENT,
        .value = current_base + (rand() % 10 - 5),  // 5±5A
        .timestamp = current_time,
        .valid = true
    };
    wave_data_list[1] = (wave_data_t){
        .type = WAVE_DATA_SPEED,
        .value = speed_base + (rand() % 10 - 5),  // 实际速度±50
        .timestamp = current_time,
        .valid = true
    };
    wave_data_list[2] = (wave_data_t){
        .type = WAVE_DATA_POSITION,
        .value = position_base++,  // 实际位置±50
        .timestamp = current_time,
        .valid = true
    };
    wave_data_list[3] = (wave_data_t){
        .type = WAVE_DATA_VOLTAGE,
        .value = voltage_base + (rand() % 20 - 10),  // 110±10V
        .timestamp = current_time,
        .valid = true
    };
    wave_data_list[4] = (wave_data_t){
        .type = WAVE_DATA_TEMPERATURE,
        .value = temp_base + (rand() % 10 - 5),  // 25±5°C
        .timestamp = current_time,
        .valid = true
    };

    // 获取当前选择的波形类型
    wave_data_type_t current_type = wave_get_display_type();

    // 只发送当前选择的波形数据
    ui_notif_msg_t ui_msg = {
        .type = UI_NOTIF_WAVE_DATA_UPDATE,
        .user_data = &wave_data_list[current_type]
    };

    // 发送到UI通知服务
    ui_notif_service_commit(&ui_msg);

    // 偶尔打印调试信息
    static uint32_t debug_counter = 0;
    if (++debug_counter % 20 == 0) {
        ESP_LOGI(TAG, "Send wave: type=%d, value=%ld", current_type, wave_data_list[current_type].value);
    }


    // 更新基础值，模拟数据变化
    // current_base = (current_base % 15) + 1;  // 1-15A循环
    // voltage_base = (voltage_base % 140) + 100;  // 100-140V循环
    // temp_base = (temp_base % 60) + 20;  // 20-80°C循环
}